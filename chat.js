// DOM Elements
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const chatMessages = document.getElementById('chatMessages');
const thinkingBtn = document.querySelector('.thinking-btn');
const uploadBtn = document.querySelector('.upload-btn');
const refreshBtn = document.querySelector('.refresh-btn');

// State
let isThinking = false;
let conversationHistory = [];

// Auto-resize textarea
messageInput.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 200) + 'px';
    
    // Enable/disable send button
    sendBtn.disabled = !this.value.trim();
});

// Send message on Enter (but not Shift+Enter)
messageInput.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
});

// Send button click
sendBtn.addEventListener('click', sendMessage);

// Thinking toggle
thinkingBtn.addEventListener('click', function() {
    isThinking = !isThinking;
    this.classList.toggle('active', isThinking);
    this.style.backgroundColor = isThinking ? '#3b82f6' : 'transparent';
    this.style.color = isThinking ? '#ffffff' : '#6b7280';
});

// Upload functionality
uploadBtn.addEventListener('click', function() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*,.pdf,.doc,.docx';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            handleFileUpload(file);
        }
    };
    input.click();
});

// Refresh functionality
refreshBtn.addEventListener('click', function() {
    if (confirm('确定要清空对话历史吗？')) {
        chatMessages.innerHTML = '';
        conversationHistory = [];
        showWelcomeMessage();
    }
});

// Send message function
function sendMessage() {
    const text = messageInput.value.trim();
    if (!text) return;
    
    // Add user message
    addMessage(text, 'user');
    
    // Clear input
    messageInput.value = '';
    messageInput.style.height = 'auto';
    sendBtn.disabled = true;
    
    // Show typing indicator
    showTypingIndicator();
    
    // Simulate AI response
    setTimeout(() => {
        hideTypingIndicator();
        const response = generateAIResponse(text);
        addMessage(response, 'assistant');
    }, 1000 + Math.random() * 2000);
}

// Add message to chat
function addMessage(content, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.textContent = sender === 'user' ? '用' : '🐬';
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);
    
    chatMessages.appendChild(messageDiv);
    
    // Hide welcome section if exists
    const welcomeSection = document.querySelector('.welcome-section');
    if (welcomeSection) {
        welcomeSection.style.display = 'none';
    }
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Add to conversation history
    conversationHistory.push({ content, sender, timestamp: new Date() });
}

// Show typing indicator
function showTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message assistant typing-indicator';
    typingDiv.id = 'typingIndicator';
    
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.textContent = '🐬';
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.innerHTML = '<span class="typing-dots">正在思考<span>.</span><span>.</span><span>.</span></span>';
    
    typingDiv.appendChild(avatar);
    typingDiv.appendChild(messageContent);
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Hide typing indicator
function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Generate AI response
function generateAIResponse(userMessage) {
    const responses = [
        '我理解您的问题。作为专业的医疗AI助手，我可以帮您分析超声图像并提供相关建议。',
        '感谢您的咨询。基于您提供的信息，我建议您寻求专业医生的进一步诊断。',
        '这是一个很好的问题。在医疗影像分析方面，我可以提供初步的观察和建议，但最终诊断仍需要专业医生确认。',
        '我注意到您的问题涉及医疗健康。请注意，我的建议仅供参考，不能替代专业医疗诊断。',
        '根据医疗AI的最佳实践，我建议您将相关检查结果提供给您的主治医生进行专业评估。'
    ];
    
    // Simple keyword-based responses
    if (userMessage.includes('超声') || userMessage.includes('图像')) {
        return '我可以帮助您分析超声图像。请上传您的超声检查图片，我将为您提供初步的观察和建议。请注意，这不能替代专业医生的诊断。';
    }
    
    if (userMessage.includes('症状') || userMessage.includes('疼痛')) {
        return '我了解您的症状描述。建议您详细记录症状的发生时间、持续时间和严重程度，并尽快咨询专业医生。如果症状严重，请及时就医。';
    }
    
    if (userMessage.includes('报告') || userMessage.includes('检查')) {
        return '我可以帮助您理解医疗报告的基本内容。请上传您的检查报告，我将为您解释相关术语和可能的含义。但请记住，最终解读应由您的医生完成。';
    }
    
    return responses[Math.floor(Math.random() * responses.length)];
}

// Handle file upload
function handleFileUpload(file) {
    const fileName = file.name;
    const fileSize = (file.size / 1024 / 1024).toFixed(2) + ' MB';
    
    // Add file upload message
    addMessage(`已上传文件: ${fileName} (${fileSize})`, 'user');
    
    // Simulate file processing
    setTimeout(() => {
        let response = '';
        if (file.type.startsWith('image/')) {
            response = '我已收到您上传的图像文件。正在分析中...请注意，这是初步分析，具体诊断请咨询专业医生。';
        } else {
            response = '我已收到您的文件。请描述您希望我关注的具体问题，这样我可以为您提供更准确的建议。';
        }
        addMessage(response, 'assistant');
    }, 1500);
}

// Show welcome message on page load
function showWelcomeMessage() {
    // Welcome message is already in HTML, no need to add it programmatically
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Focus on input
    messageInput.focus();
    
    // Initialize send button state
    sendBtn.disabled = true;
    
    // Add smooth entrance animation
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.5s ease';
        document.body.style.opacity = '1';
    }, 100);
});

// Sidebar functionality
document.querySelectorAll('.sidebar-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Add ripple effect
        const ripple = document.createElement('div');
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.background = 'rgba(255, 255, 255, 0.1)';
        ripple.style.transform = 'scale(0)';
        ripple.style.animation = 'ripple 0.6s linear';
        ripple.style.left = '50%';
        ripple.style.top = '50%';
        ripple.style.width = '20px';
        ripple.style.height = '20px';
        ripple.style.marginLeft = '-10px';
        ripple.style.marginTop = '-10px';
        
        this.style.position = 'relative';
        this.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
});

// New Chat functionality
document.getElementById('newChatBtn').addEventListener('click', function() {
    if (confirm('开始新对话吗？当前对话将被保存到历史记录中。')) {
        // Clear current chat
        chatMessages.innerHTML = '';
        conversationHistory = [];
        
        // Show welcome message
        document.querySelector('.welcome-section').style.display = 'block';
        
        // Add to history
        addToHistory('新对话 ' + new Date().toLocaleTimeString());
    }
});

// Favorite functionality
document.getElementById('favoriteBtn').addEventListener('click', function() {
    alert('收藏功能开发中...');
});

// History item functionality
function setupHistoryItems() {
    document.querySelectorAll('.history-item').forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            document.querySelectorAll('.history-item').forEach(i => i.classList.remove('active'));
            // Add active class to clicked item
            this.classList.add('active');
            
            // Load conversation (simulate)
            const historyText = this.querySelector('.history-text').textContent;
            loadConversation(historyText);
        });
    });
}

// Add conversation to history
function addToHistory(title) {
    const todaySection = document.querySelector('.history-section');
    const newItem = document.createElement('div');
    newItem.className = 'history-item';
    newItem.innerHTML = `<span class="history-text">${title}</span>`;
    
    // Insert after the title
    const titleElement = todaySection.querySelector('.history-title');
    titleElement.insertAdjacentElement('afterend', newItem);
    
    // Setup click handler
    newItem.addEventListener('click', function() {
        document.querySelectorAll('.history-item').forEach(i => i.classList.remove('active'));
        this.classList.add('active');
        loadConversation(title);
    });
}

// Load conversation from history
function loadConversation(title) {
    // Simulate loading conversation
    chatMessages.innerHTML = '';
    document.querySelector('.welcome-section').style.display = 'none';
    
    // Add sample messages
    setTimeout(() => {
        addMessage('你好，我需要帮助分析这份超声报告', 'user');
        setTimeout(() => {
            addMessage('我很乐意帮助您分析超声报告。请上传您的报告图片，我将为您提供专业的解读。', 'assistant');
        }, 1000);
    }, 500);
}

// Add CSS for typing animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .typing-dots span {
        animation: typing 1.4s infinite;
    }
    
    .typing-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .typing-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    @keyframes typing {
        0%, 60%, 100% {
            opacity: 0.3;
        }
        30% {
            opacity: 1;
        }
    }
    
    .action-btn.active {
        background-color: #3b82f6 !important;
        color: #ffffff !important;
    }
`;
document.head.appendChild(style);
