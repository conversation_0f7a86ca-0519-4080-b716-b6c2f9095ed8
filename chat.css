/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f7f7f8;
    color: #374151;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* Sidebar Styles */
.sidebar {
    width: 260px;
    background-color: #171717;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e5e7eb;
}

.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #2d2d2d;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-text {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.sidebar-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.sidebar-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 24px;
}

.sidebar-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 12px;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: #9ca3af;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    text-align: left;
    width: 100%;
}

.sidebar-btn:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

.sidebar-btn span {
    font-size: 13px;
}

/* Chat History Styles */
.chat-history {
    flex: 1;
}

.history-section {
    margin-bottom: 16px;
}

.history-title {
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 12px;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.history-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.history-item:hover {
    background-color: #2d2d2d;
}

.history-item.active {
    background-color: #3b82f6;
}

.history-item.active .history-text {
    color: #ffffff;
}

.history-text {
    color: #9ca3af;
    font-size: 13px;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
}

.history-item:hover .history-text {
    color: #ffffff;
}

.sidebar-footer {
    padding: 16px;
    border-top: 1px solid #2d2d2d;
}

.footer-actions {
    margin-bottom: 12px;
}

.footer-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 8px 12px;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: #9ca3af;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
}

.footer-btn:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;
}

.user-info:hover {
    background-color: #2d2d2d;
}

.user-avatar {
    width: 28px;
    height: 28px;
    background-color: #4ade80;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    color: #ffffff;
    flex-shrink: 0;
}

.user-name {
    color: #ffffff;
    font-size: 13px;
    font-weight: 500;
    flex: 1;
}

.status-indicator {
    color: #10b981;
    flex-shrink: 0;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    position: relative;
}

.chat-header {
    display: flex;
    justify-content: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #ffffff;
}

.header-logo {
    display: flex;
    align-items: center;
}

.dolphin-logo {
    font-size: 24px;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0 24px;
}

.welcome-section {
    max-width: 768px;
    margin: 120px auto 40px;
    text-align: center;
}

.welcome-title {
    font-size: 32px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
    line-height: 1.2;
}

.welcome-subtitle {
    font-size: 18px;
    color: #6b7280;
    line-height: 1.5;
}

.chat-messages {
    flex: 1;
    max-width: 768px;
    margin: 0 auto;
    width: 100%;
}

/* Input Section Styles */
.input-section {
    background-color: #ffffff;
    border-top: 1px solid #e5e7eb;
    padding: 24px;
}

.input-container {
    max-width: 768px;
    margin: 0 auto;
}

.input-wrapper {
    position: relative;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 24px;
    padding: 12px 16px;
    display: flex;
    align-items: flex-end;
    gap: 8px;
    transition: all 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: #ffffff;
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    resize: none;
    font-size: 16px;
    line-height: 1.5;
    color: #1f2937;
    min-height: 24px;
    max-height: 200px;
    font-family: inherit;
}

#messageInput::placeholder {
    color: #9ca3af;
}

.input-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #e5e7eb;
    color: #374151;
}

.send-btn {
    background-color: #1f2937;
    color: #ffffff;
}

.send-btn:hover {
    background-color: #111827;
}

.send-btn:disabled {
    background-color: #e5e7eb;
    color: #9ca3af;
    cursor: not-allowed;
}

.input-footer {
    margin-top: 16px;
    text-align: center;
}

.footer-text {
    font-size: 12px;
    color: #9ca3af;
}

/* Message Styles */
.message {
    margin-bottom: 24px;
    display: flex;
    gap: 16px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background-color: #3b82f6;
    color: #ffffff;
}

.message.assistant .message-avatar {
    background-color: #10b981;
    color: #ffffff;
}

.message-content {
    flex: 1;
    background-color: #f9fafb;
    padding: 16px;
    border-radius: 16px;
    font-size: 15px;
    line-height: 1.6;
}

.message.user .message-content {
    background-color: #3b82f6;
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }
    
    .welcome-title {
        font-size: 24px;
    }
    
    .welcome-subtitle {
        font-size: 16px;
    }
    
    .input-section {
        padding: 16px;
    }
    
    .chat-container {
        padding: 0 16px;
    }
}

@media (max-width: 640px) {
    .sidebar {
        display: none;
    }
    
    .app-container {
        flex-direction: column;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.welcome-section {
    animation: fadeIn 0.8s ease-out;
}

.message {
    animation: fadeIn 0.4s ease-out;
}

/* Scrollbar Styling */
.chat-container::-webkit-scrollbar {
    width: 6px;
}

.chat-container::-webkit-scrollbar-track {
    background: transparent;
}

.chat-container::-webkit-scrollbar-thumb {
    background: #e5e7eb;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: #d1d5db;
}
