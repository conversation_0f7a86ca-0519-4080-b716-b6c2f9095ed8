<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dolphin AI - 智能医疗助手</title>
    <link rel="stylesheet" href="chat.css">
</head>
<body>
    <div class="app-container">
        <!-- Left Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <span class="logo-text">LOGO</span>
                </div>
            </div>
            
            <div class="sidebar-content">
                <!-- Action Buttons -->
                <div class="sidebar-actions">
                    <button class="sidebar-btn" id="favoriteBtn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="16,18 22,12 16,6"/>
                            <polyline points="8,6 2,12 8,18"/>
                        </svg>
                        <span>收藏历史</span>
                    </button>
                    <button class="sidebar-btn" id="newChatBtn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 12a9 9 0 11-6.219-8.56"/>
                        </svg>
                        <span>开启新对话</span>
                    </button>
                </div>
                
                <!-- Chat History -->
                <div class="chat-history">
                    <div class="history-section">
                        <div class="history-title">今天</div>
                        <div class="history-item active">
                            <span class="history-text">出差为期史记录</span>
                        </div>
                    </div>
                    
                    <div class="history-section">
                        <div class="history-title">昨天</div>
                        <div class="history-item">
                            <span class="history-text">出差为期史记录</span>
                        </div>
                        <div class="history-item">
                            <span class="history-text">出差为期史记录</span>
                        </div>
                    </div>
                    
                    <div class="history-section">
                        <div class="history-title">7天以内</div>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-footer">
                <div class="footer-actions">
                    <button class="footer-btn">
                        <span>用户</span>
                        <span>个人偏好与设置</span>
                    </button>
                </div>
                
                <div class="user-info">
                    <div class="user-avatar">
                        <span>用户</span>
                    </div>
                    <span class="user-name">头像</span>
                    <div class="status-indicator">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <circle cx="12" cy="12" r="10"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="main-content">
            <div class="chat-header">
                <div class="header-logo">
                    <span class="dolphin-logo">🐬</span>
                </div>
            </div>
            
            <div class="chat-container">
                <div class="welcome-section">
                    <h1 class="welcome-title">我是Dolphin AI，很高兴能帮助您</h1>
                    <p class="welcome-subtitle">我可以帮助您检查超声图像，并提供医疗建议</p>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <!-- Messages will be added here -->
                </div>
            </div>
            
            <div class="input-section">
                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea 
                            id="messageInput" 
                            placeholder="给Dolphin AI发送信息"
                            rows="1"
                        ></textarea>
                        <div class="input-actions">
                            <button class="action-btn thinking-btn" title="深度思考">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M12 1v6"/>
                                    <path d="M12 17v6"/>
                                    <path d="m4.22 4.22 4.24 4.24"/>
                                    <path d="m15.54 15.54 4.24 4.24"/>
                                    <path d="M1 12h6"/>
                                    <path d="M17 12h6"/>
                                    <path d="m4.22 19.78 4.24-4.24"/>
                                    <path d="m15.54 8.46 4.24-4.24"/>
                                </svg>
                            </button>
                            <button class="action-btn upload-btn" title="上传文件">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10,9 9,9 8,9"/>
                                </svg>
                            </button>
                            <button class="action-btn refresh-btn" title="刷新">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23 4 23 10 17 10"/>
                                    <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"/>
                                </svg>
                            </button>
                            <button class="send-btn" id="sendBtn" title="发送">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="22" y1="2" x2="11" y2="13"/>
                                    <polygon points="22,2 15,22 11,13 2,9"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="input-footer">
                    <p class="footer-text">深度思考可能可选择开启或者关闭</p>
                </div>
            </div>
        </div>
    </div>

    <script src="chat.js"></script>
</body>
</html>
