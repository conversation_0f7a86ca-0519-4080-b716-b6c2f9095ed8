// DOM elements
const methodButtons = document.querySelectorAll('.method-btn');
const loginInput = document.getElementById('loginInput');
const verifyBtn = document.querySelector('.verify-btn');
const loginBtn = document.querySelector('.login-btn');

// Login method switching
methodButtons.forEach(button => {
    button.addEventListener('click', () => {
        // Remove active class from all buttons
        methodButtons.forEach(btn => btn.classList.remove('active'));
        // Add active class to clicked button
        button.classList.add('active');
        
        // Update input placeholder based on selected method
        const method = button.dataset.method;
        updateInputPlaceholder(method);
    });
});

// Update input placeholder
function updateInputPlaceholder(method) {
    switch(method) {
        case 'phone':
            loginInput.placeholder = '请输入手机号';
            loginInput.type = 'tel';
            break;
        case 'email':
            loginInput.placeholder = '请输入邮箱';
            loginInput.type = 'email';
            break;
        case 'wechat':
            loginInput.placeholder = '请输入微信号';
            loginInput.type = 'text';
            break;
    }
}

// Verification code functionality
let countdown = 0;
verifyBtn.addEventListener('click', () => {
    if (countdown > 0) return;
    
    // Validate input
    const inputValue = loginInput.value.trim();
    if (!inputValue) {
        alert('请先输入手机号/邮箱');
        return;
    }
    
    // Start countdown
    startCountdown();
    
    // Simulate sending verification code
    console.log('发送验证码到：', inputValue);
});

function startCountdown() {
    countdown = 60;
    verifyBtn.disabled = true;
    verifyBtn.textContent = `${countdown}秒后重发`;
    
    const timer = setInterval(() => {
        countdown--;
        if (countdown > 0) {
            verifyBtn.textContent = `${countdown}秒后重发`;
        } else {
            verifyBtn.textContent = '获取验证码';
            verifyBtn.disabled = false;
            clearInterval(timer);
        }
    }, 1000);
}

// Login functionality
loginBtn.addEventListener('click', () => {
    const loginValue = loginInput.value.trim();
    const verificationCode = document.querySelector('.verification-input').value.trim();
    
    if (!loginValue) {
        alert('请输入登录信息');
        return;
    }
    
    if (!verificationCode) {
        alert('请输入验证码');
        return;
    }
    
    // Show loading state
    loginBtn.textContent = '登录中...';
    loginBtn.disabled = true;
    
    // Simulate login request
    setTimeout(() => {
        loginBtn.textContent = '登录';
        loginBtn.disabled = false;
        alert('登录成功！');
    }, 1500);
});

// Input validation
loginInput.addEventListener('input', (e) => {
    const method = document.querySelector('.method-btn.active').dataset.method;
    const value = e.target.value;
    
    // Real-time validation based on method
    if (method === 'phone') {
        // Remove non-numeric characters
        e.target.value = value.replace(/[^\d]/g, '');
        // Limit to 11 digits
        if (e.target.value.length > 11) {
            e.target.value = e.target.value.slice(0, 11);
        }
    }
});

// Enhanced input focus effects
document.querySelectorAll('input').forEach(input => {
    input.addEventListener('focus', () => {
        input.parentElement.classList.add('focused');
    });
    
    input.addEventListener('blur', () => {
        input.parentElement.classList.remove('focused');
    });
});

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    // Set initial state
    updateInputPlaceholder('phone');
    
    // Add smooth transitions
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.5s ease';
        document.body.style.opacity = '1';
    }, 100);
});
