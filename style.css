/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
}

.container {
    width: 100%;
    max-width: 480px;
    padding: 20px;
}

/* Logo Section */
.logo-section {
    text-align: center;
    margin-bottom: 40px;
}

.logo {
    font-size: 36px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 2px;
}

/* Login Form */
.login-wrapper {
    margin-bottom: 30px;
}

.login-card {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Login Methods Toggle */
.login-methods {
    display: flex;
    background: #f5f7fa;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 24px;
}

.method-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.method-btn.active {
    background: white;
    color: #4a90e2;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
}

.method-btn:hover:not(.active) {
    color: #4a90e2;
}

/* Input Fields */
.input-group {
    margin-bottom: 20px;
}

.input-field {
    width: 100%;
    padding: 16px;
    border: 2px solid #e8ecf0;
    border-radius: 12px;
    font-size: 16px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    background: #fafbfc;
}

.input-field:focus {
    outline: none;
    border-color: #4a90e2;
    background: white;
    box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.1);
}

.verification-row {
    display: flex;
    gap: 12px;
}

.verification-input {
    flex: 1;
    padding: 16px;
    border: 2px solid #e8ecf0;
    border-radius: 12px;
    font-size: 16px;
    background: #fafbfc;
    transition: all 0.3s ease;
}

.verification-input:focus {
    outline: none;
    border-color: #4a90e2;
    background: white;
    box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.1);
}

.verify-btn {
    padding: 16px 20px;
    border: none;
    background: #4a90e2;
    color: white;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.verify-btn:hover {
    background: #357abd;
    transform: translateY(-1px);
}

/* Agreement Text */
.agreement-text {
    font-size: 12px;
    color: #888;
    line-height: 1.5;
    margin-bottom: 24px;
    text-align: center;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 16px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 24px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

/* WeChat Section */
.wechat-section {
    display: flex;
    align-items: center;
    gap: 16px;
    padding-top: 20px;
    border-top: 1px solid #e8ecf0;
}

.wechat-qr {
    width: 60px;
    height: 60px;
    border: 2px solid #e8ecf0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafbfc;
}

.qr-placeholder {
    font-size: 10px;
    color: #999;
    text-align: center;
}

.wechat-text {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
}

.wechat-icon {
    width: 20px;
    height: 20px;
}

/* Footer */
.footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 16px;
    }
    
    .login-card {
        padding: 24px;
    }
    
    .logo {
        font-size: 28px;
    }
    
    .verification-row {
        flex-direction: column;
        gap: 8px;
    }
    
    .verify-btn {
        width: 100%;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-card {
    animation: fadeIn 0.6s ease-out;
}

.logo {
    animation: fadeIn 0.8s ease-out;
}
